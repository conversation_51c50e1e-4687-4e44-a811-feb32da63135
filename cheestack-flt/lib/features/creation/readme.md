# 创作模块技术文档

## 📋 模块概述

创作模块是CheeStack应用的核心功能模块，负责书籍和卡片的创建、编辑、管理和同步。采用本地优先(Local-First)的设计理念，确保用户在离线状态下也能正常使用所有创作功能，并在网络恢复时自动同步到云端。

## 🎯 核心功能

### 1. 书籍管理
- **创建与编辑**：支持本地优先的书籍创建和编辑，包含封面图片处理
- **列表管理**：提供搜索、筛选、排序功能，基于本地数据库检索
- **详情展示**：展示书籍统计、学习进度、最近学习记录等信息
- **批量操作**：支持批量删除、移动、复制等操作

### 2. 卡片管理
- **多类型支持**：支持基础卡片、选择题、填空题等多种卡片类型
- **资源管理**：支持图片、音频、视频等多媒体资源
- **智能搜索**：基于标题、问题、答案的全文搜索
- **分类整理**：支持按书籍、类型、难度等维度分类

### 3. 数据同步
- **本地优先**：所有操作优先保存到本地SQLite数据库
- **自动同步**：网络可用时自动同步到云端PostgreSQL
- **冲突解决**：智能检测并解决数据冲突
- **状态管理**：提供清晰的同步状态反馈

### 4. 离线能力
- **完全离线**：无网络时可正常创建、编辑、删除内容
- **操作记录**：自动记录离线操作，网络恢复后批量同步
- **数据安全**：本地数据加密存储，防止数据丢失

## 🏗️ 技术架构

### 分层架构
```
┌─────────────────────────────────────┐
│         Presentation Layer          │  ← Pages, Widgets, Controllers
├─────────────────────────────────────┤
│         Application Layer           │  ← Services, Use Cases
├─────────────────────────────────────┤
│           Domain Layer              │  ← Models, Entities
├─────────────────────────────────────┤
│       Infrastructure Layer          │  ← Database, Network, Storage
└─────────────────────────────────────┘
```

### 核心组件

#### 控制器层 (Controllers)
- **CreationController**: 主控制器，管理整体创作状态
- **BookController**: 书籍编辑控制器，处理书籍CRUD操作
- **BookDetailController**: 书籍详情控制器，展示书籍统计信息

#### 服务层 (Services)
- **BookDataService**: 书籍本地数据服务
- **CardDataService**: 卡片本地数据服务
- **ApiSyncService**: 数据同步服务
- **BookService/CardService**: 网络API服务

#### 数据层 (Data)
- **本地数据库**: SQLite + sqflite
- **云端数据库**: PostgreSQL + Tortoise ORM
- **数据模型**: BookModel, CardModel, CardAsset等

## 📁 文件结构

```
lib/features/creation/
├── controllers/                 # 控制器层
│   ├── creation_controller.dart # 主控制器
│   ├── book_controller.dart     # 书籍控制器
│   └── book_detail_controller.dart # 书籍详情控制器
├── pages/                      # 页面层
│   ├── creation_page.dart      # 创作主页
│   ├── book_list_page.dart     # 书籍列表页
│   ├── book_edit_page.dart     # 书籍编辑页
│   ├── book_detail_page.dart   # 书籍详情页
│   ├── card_list_page.dart     # 卡片列表页
│   └── widgets/                # 共享组件
│       ├── book_card.dart      # 书籍卡片组件
│       ├── search_bar.dart     # 搜索栏组件
│       └── book_filter_bar.dart # 筛选栏组件
├── apis/                       # API服务层
│   ├── book_service.dart       # 书籍网络服务
│   ├── book_api.dart          # 书籍API接口
│   ├── card_service.dart       # 卡片网络服务
│   └── card_api.dart          # 卡片API接口
├── models/                     # 数据模型（如需要）
├── readme.md                   # 本文档
└── index.dart                  # 模块导出文件
```

## 🔄 数据流设计

### 本地优先流程
```
User Action → Controller → DataService → Local SQLite → UI Update
                    ↓
              Background Sync → Remote API → Cloud Database
```

### 同步机制
1. **写入本地**: 所有操作首先写入本地数据库
2. **标记状态**: 设置`is_dirty`标记表示需要同步
3. **后台同步**: 异步同步到云端，不阻塞UI
4. **状态更新**: 同步完成后更新本地状态
5. **冲突处理**: 检测并解决数据冲突

## 🔧 开发指南

### 1. 本地优先原则
```dart
// ✅ 正确：先保存到本地
final book = await bookDataService.createBook(
  name: name,
  brief: brief,
);
// 后台异步同步
unawaited(syncService.syncBook(book));

// ❌ 错误：直接调用API
final book = await bookApi.createBook(data);
```

### 2. 错误处理
```dart
try {
  final result = await bookDataService.createBook(data);
  if (result != null) {
    ShowToast.success('书籍已保存到本地');
  } else {
    throw Exception('本地保存失败');
  }
} catch (e) {
  final friendlyMessage = ErrorHandler.getUserFriendlyMessage(e);
  ShowToast.fail(friendlyMessage);
}
```

### 3. 状态管理
```dart
class CreationController extends GetxController {
  // 使用响应式变量
  final RxBool isLoading = false.obs;
  final RxList<BookModel> bookList = <BookModel>[].obs;
  
  // 使用GetBuilder进行局部更新
  void updateBookList() {
    update(['book_list']);
  }
}
```

## 📚 相关文档

- **实现指导**: `docs/04-implementation/creation/README.md`
- **前端实现**: `docs/04-implementation/creation/frontend-implementation.md`
- **后端实现**: `docs/04-implementation/creation/backend-implementation.md`
- **数据库设计**: `docs/04-implementation/creation/database-design.md`
- **API集成**: `docs/04-implementation/creation/api-integration.md`
- **测试指导**: `docs/04-implementation/creation/testing-guide.md`

## 🚀 快速开始

### 1. 初始化服务
```dart
// 在应用启动时初始化数据服务
await Get.putAsync(() => DatabaseService().init());
await Get.putAsync(() => BookDataService().init());
await Get.putAsync(() => CardDataService().init());
```

### 2. 使用控制器
```dart
// 在页面中使用控制器
class CreationPage extends GetView<CreationController> {
  @override
  Widget build(BuildContext context) {
    return GetBuilder<CreationController>(
      builder: (controller) => Scaffold(
        body: controller.isLoading 
          ? LoadingWidget() 
          : ContentWidget(),
      ),
    );
  }
}
```

### 3. 数据操作
```dart
// 创建书籍
final book = await Get.find<BookDataService>().createBook(
  name: '新书籍',
  brief: '书籍简介',
  privacy: 'free',
);

// 搜索书籍
final books = await Get.find<BookDataService>().searchBooks('关键词');
```

## ⚠️ 注意事项

1. **本地优先**: 所有数据操作必须优先使用本地数据服务
2. **异步同步**: 同步操作不应阻塞UI线程
3. **错误处理**: 提供用户友好的错误提示
4. **状态管理**: 合理使用响应式编程，避免过度更新
5. **测试覆盖**: 确保核心功能有充分的测试覆盖

## 🔄 技术实现要点

### 本地数据库
- 使用sqflite，通过DaoManager、cardDao、bookDao等实现本地数据的增删改查
- CardDataService、UserDataService等服务负责业务逻辑与本地存储
- 所有创建/编辑操作必须先写入本地数据库，再异步同步到云端

### 同步机制
- ApiSyncService/SyncService负责本地与云端的数据同步
- 支持全量/增量同步，记录同步状态
- 新建/编辑/删除操作后，自动触发同步（可配置为手动）

### 代码结构建议
- 控制器（如CreationController、BookController）只负责UI与业务调度
- 具体数据操作全部通过本地服务（如CardDataService、UserDataService）完成
- 网络请求与本地数据库操作解耦，便于单元测试与维护

## 📈 优化建议

### 强制本地优先写入
- 所有卡片、书籍的创建/编辑/删除，必须先调用本地数据库服务
- UI层面应有"同步中/失败/成功"状态提示

### 离线操作队列
- 设计本地操作队列，记录所有未同步的变更
- 网络恢复后自动批量同步，保证数据一致性

### 冲突检测与提示
- 同步时如遇到本地与云端数据冲突，优先保留本地修改
- 提示用户可手动处理冲突情况

### 本地数据加密与备份
- 重要数据（如卡片内容、书籍信息）可考虑本地加密存储
- 支持本地数据导出/导入，便于用户迁移与备份

### AI生成内容的本地落盘
- AI生成的新内容也应先写入本地数据库
- 避免因网络波动导致数据丢失

### 单元测试与异常处理
- 增加本地数据库相关的单元测试，确保各种异常场景下数据不丢失
- 对本地数据库操作失败要有兜底方案（如重试、错误提示等）

---

**版本**: 1.0.0  
**最后更新**: 2024-01-01  
**维护者**: CheeStack开发团队
