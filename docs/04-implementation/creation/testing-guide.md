# 创作模块测试指导

## 📋 概述

本文档详细说明创作模块的测试策略和实现方案，包含单元测试、集成测试、UI测试的具体实现方法和测试用例。遵循测试驱动开发(TDD)原则，确保代码质量和功能可靠性。

## 🧪 测试架构

### 测试金字塔
```
                    ┌─────────────────┐
                    │   E2E Tests     │  ← 端到端测试 (少量)
                    │   (Integration) │
                ┌───┴─────────────────┴───┐
                │    Widget Tests         │  ← UI组件测试 (适量)
                │    (Component)          │
            ┌───┴─────────────────────────┴───┐
            │        Unit Tests               │  ← 单元测试 (大量)
            │        (Logic)                  │
            └─────────────────────────────────┘
```

### 测试分层策略
- **单元测试 (70%)**：测试业务逻辑、数据服务、工具函数
- **组件测试 (20%)**：测试UI组件、页面交互、状态管理
- **集成测试 (10%)**：测试完整用户流程、API集成、数据同步

## 🔧 测试环境配置

### 1. 依赖配置

```yaml
# pubspec.yaml
dev_dependencies:
  flutter_test:
    sdk: flutter
  mockito: ^5.4.0
  build_runner: ^2.4.0
  test: ^1.24.0
  integration_test:
    sdk: flutter
  patrol: ^2.0.0
  golden_toolkit: ^0.15.0
  network_image_mock: ^2.1.1
```

### 2. 测试目录结构

```
test/
├── unit/                           # 单元测试
│   ├── controllers/
│   │   ├── creation_controller_test.dart
│   │   ├── book_controller_test.dart
│   │   └── book_detail_controller_test.dart
│   ├── services/
│   │   ├── book_service_test.dart
│   │   ├── card_service_test.dart
│   │   └── sync_service_test.dart
│   └── models/
│       ├── book_model_test.dart
│       └── card_model_test.dart
├── widget/                         # 组件测试
│   ├── pages/
│   │   ├── creation_page_test.dart
│   │   ├── book_list_page_test.dart
│   │   └── book_edit_page_test.dart
│   └── widgets/
│       ├── book_card_test.dart
│       └── search_bar_test.dart
├── integration/                    # 集成测试
│   ├── book_management_test.dart
│   ├── card_management_test.dart
│   └── data_sync_test.dart
├── fixtures/                       # 测试数据
│   ├── books.json
│   ├── cards.json
│   └── users.json
├── mocks/                         # Mock对象
│   ├── mock_services.dart
│   └── mock_repositories.dart
└── helpers/                       # 测试辅助工具
    ├── test_helpers.dart
    └── widget_test_helpers.dart
```

## 🧩 单元测试实现

### 1. 控制器测试

```dart
// test/unit/controllers/creation_controller_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:get/get.dart';

import 'package:cheestack_flt/features/creation/controllers/creation_controller.dart';
import 'package:cheestack_flt/services/book_data_service.dart';
import 'package:cheestack_flt/services/card_data_service.dart';
import 'package:cheestack_flt/models/book.dart';

import 'creation_controller_test.mocks.dart';

@GenerateMocks([BookDataService, CardDataService])
void main() {
  group('CreationController Tests', () {
    late CreationController controller;
    late MockBookDataService mockBookDataService;
    late MockCardDataService mockCardDataService;

    setUp(() {
      // 初始化Mock对象
      mockBookDataService = MockBookDataService();
      mockCardDataService = MockCardDataService();
      
      // 注册Mock服务
      Get.put<BookDataService>(mockBookDataService);
      Get.put<CardDataService>(mockCardDataService);
      
      // 创建控制器
      controller = CreationController();
    });

    tearDown(() {
      Get.reset();
    });

    group('loadCreationStats', () {
      test('should load books and cards successfully', () async {
        // Arrange
        final mockBooks = [
          BookModel(id: 1, name: 'Test Book 1'),
          BookModel(id: 2, name: 'Test Book 2'),
        ];
        final mockCards = [
          CardModel(id: 1, title: 'Test Card 1'),
          CardModel(id: 2, title: 'Test Card 2'),
        ];

        when(mockBookDataService.getUserBooks())
            .thenAnswer((_) async => mockBooks);
        when(mockCardDataService.getUserCards())
            .thenAnswer((_) async => mockCards);

        // Act
        await controller.loadCreationStats();

        // Assert
        expect(controller.bookList, equals(mockBooks));
        expect(controller.cardList, equals(mockCards));
        expect(controller.totalBooks, equals(2));
        expect(controller.totalCards, equals(2));
        expect(controller.isLoading, isFalse);
        
        verify(mockBookDataService.getUserBooks()).called(1);
        verify(mockCardDataService.getUserCards()).called(1);
      });

      test('should handle error when loading fails', () async {
        // Arrange
        when(mockBookDataService.getUserBooks())
            .thenThrow(Exception('Network error'));

        // Act
        await controller.loadCreationStats();

        // Assert
        expect(controller.isLoading, isFalse);
        expect(controller.hasError, isTrue);
        expect(controller.errorMessage, contains('Network error'));
      });
    });

    group('searchBooks', () {
      test('should filter books by search keyword', () async {
        // Arrange
        final mockBooks = [
          BookModel(id: 1, name: 'Flutter Guide'),
          BookModel(id: 2, name: 'Dart Programming'),
          BookModel(id: 3, name: 'Mobile Development'),
        ];

        when(mockBookDataService.searchBooks('Flutter'))
            .thenAnswer((_) async => [mockBooks[0]]);

        // Act
        await controller.searchBooks('Flutter');

        // Assert
        expect(controller.bookList.length, equals(1));
        expect(controller.bookList[0].name, equals('Flutter Guide'));
        verify(mockBookDataService.searchBooks('Flutter')).called(1);
      });
    });

    group('createBook', () {
      test('should navigate to book edit page and refresh on success', () async {
        // Arrange
        final newBook = BookModel(id: 3, name: 'New Book');
        
        // Mock Get.toNamed to return the new book
        Get.testMode = true;
        
        // Act
        await controller.createBook();

        // Assert
        // 验证导航被调用
        // 注意：在实际测试中，需要mock Get.toNamed的行为
      });
    });
  });
}
```

### 2. 服务层测试

```dart
// test/unit/services/book_service_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:cheestack_flt/features/creation/apis/book_service.dart';
import 'package:cheestack_flt/services/book_data_service.dart';
import 'package:cheestack_flt/models/book.dart';

@GenerateMocks([BookDataService])
void main() {
  group('BookService Tests', () {
    late BookService bookService;
    late MockBookDataService mockBookDataService;

    setUp(() {
      mockBookDataService = MockBookDataService();
      bookService = BookService();
      
      // 注入依赖
      Get.put<BookDataService>(mockBookDataService);
    });

    tearDown(() {
      Get.reset();
    });

    group('getBookList', () {
      test('should return books from local service when available', () async {
        // Arrange
        final expectedBooks = [
          BookModel(id: 1, name: 'Local Book 1'),
          BookModel(id: 2, name: 'Local Book 2'),
        ];

        when(mockBookDataService.getUserBooks(
          orderBy: anyNamed('orderBy'),
          limit: anyNamed('limit'),
          offset: anyNamed('offset'),
        )).thenAnswer((_) async => expectedBooks);

        // Act
        final result = await bookService.getBookList();

        // Assert
        expect(result, equals(expectedBooks));
        verify(mockBookDataService.getUserBooks(
          orderBy: 'created_at DESC',
          limit: 20,
          offset: 0,
        )).called(1);
      });

      test('should handle search parameter correctly', () async {
        // Arrange
        const searchKeyword = 'Flutter';
        final expectedBooks = [
          BookModel(id: 1, name: 'Flutter Guide'),
        ];

        when(mockBookDataService.searchBooks(
          searchKeyword,
          limit: anyNamed('limit'),
          offset: anyNamed('offset'),
        )).thenAnswer((_) async => expectedBooks);

        // Act
        final result = await bookService.getBookList(search: searchKeyword);

        // Assert
        expect(result, equals(expectedBooks));
        verify(mockBookDataService.searchBooks(
          searchKeyword,
          limit: 20,
          offset: 0,
        )).called(1);
      });
    });

    group('createBook', () {
      test('should create book using local service', () async {
        // Arrange
        final bookData = {
          'name': 'New Book',
          'brief': 'Book description',
          'privacy': 'free',
        };
        final expectedBook = BookModel(
          id: 1,
          name: 'New Book',
          brief: 'Book description',
          privacy: 'free',
        );

        when(mockBookDataService.createBook(
          name: anyNamed('name'),
          brief: anyNamed('brief'),
          cover: anyNamed('cover'),
          privacy: anyNamed('privacy'),
        )).thenAnswer((_) async => expectedBook);

        // Act
        final result = await bookService.createBook(bookData, null);

        // Assert
        expect(result, equals(expectedBook));
        verify(mockBookDataService.createBook(
          name: 'New Book',
          brief: 'Book description',
          cover: null,
          privacy: 'free',
        )).called(1);
      });
    });
  });
}
```

### 3. 数据模型测试

```dart
// test/unit/models/book_model_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:cheestack_flt/models/book.dart';

void main() {
  group('BookModel Tests', () {
    group('fromJson', () {
      test('should create BookModel from valid JSON', () {
        // Arrange
        final json = {
          'id': 1,
          'name': 'Test Book',
          'brief': 'Test Description',
          'cover': 'https://example.com/cover.jpg',
          'privacy': 'free',
          'created_at': '2024-01-01T12:00:00Z',
          'updated_at': '2024-01-01T12:00:00Z',
        };

        // Act
        final book = BookModel.fromJson(json);

        // Assert
        expect(book.id, equals(1));
        expect(book.name, equals('Test Book'));
        expect(book.brief, equals('Test Description'));
        expect(book.cover, equals('https://example.com/cover.jpg'));
        expect(book.privacy, equals('free'));
        expect(book.createdAt, equals('2024-01-01T12:00:00Z'));
        expect(book.updatedAt, equals('2024-01-01T12:00:00Z'));
      });

      test('should handle null values gracefully', () {
        // Arrange
        final json = {
          'id': 1,
          'name': 'Test Book',
          'brief': null,
          'cover': null,
          'privacy': 'free',
        };

        // Act
        final book = BookModel.fromJson(json);

        // Assert
        expect(book.id, equals(1));
        expect(book.name, equals('Test Book'));
        expect(book.brief, isNull);
        expect(book.cover, isNull);
        expect(book.privacy, equals('free'));
      });
    });

    group('toJson', () {
      test('should convert BookModel to JSON', () {
        // Arrange
        final book = BookModel(
          id: 1,
          name: 'Test Book',
          brief: 'Test Description',
          cover: 'https://example.com/cover.jpg',
          privacy: 'free',
          createdAt: '2024-01-01T12:00:00Z',
          updatedAt: '2024-01-01T12:00:00Z',
        );

        // Act
        final json = book.toJson();

        // Assert
        expect(json['id'], equals(1));
        expect(json['name'], equals('Test Book'));
        expect(json['brief'], equals('Test Description'));
        expect(json['cover'], equals('https://example.com/cover.jpg'));
        expect(json['privacy'], equals('free'));
        expect(json['created_at'], equals('2024-01-01T12:00:00Z'));
        expect(json['updated_at'], equals('2024-01-01T12:00:00Z'));
      });
    });

    group('copyWith', () {
      test('should create new instance with updated values', () {
        // Arrange
        final originalBook = BookModel(
          id: 1,
          name: 'Original Name',
          brief: 'Original Brief',
          privacy: 'free',
        );

        // Act
        final updatedBook = originalBook.copyWith(
          name: 'Updated Name',
          brief: 'Updated Brief',
        );

        // Assert
        expect(updatedBook.id, equals(1));
        expect(updatedBook.name, equals('Updated Name'));
        expect(updatedBook.brief, equals('Updated Brief'));
        expect(updatedBook.privacy, equals('free'));
        
        // 原对象不应该被修改
        expect(originalBook.name, equals('Original Name'));
        expect(originalBook.brief, equals('Original Brief'));
      });
    });

    group('equality', () {
      test('should be equal when all properties are same', () {
        // Arrange
        final book1 = BookModel(
          id: 1,
          name: 'Test Book',
          brief: 'Test Brief',
          privacy: 'free',
        );
        final book2 = BookModel(
          id: 1,
          name: 'Test Book',
          brief: 'Test Brief',
          privacy: 'free',
        );

        // Act & Assert
        expect(book1, equals(book2));
        expect(book1.hashCode, equals(book2.hashCode));
      });

      test('should not be equal when properties differ', () {
        // Arrange
        final book1 = BookModel(
          id: 1,
          name: 'Test Book 1',
          privacy: 'free',
        );
        final book2 = BookModel(
          id: 1,
          name: 'Test Book 2',
          privacy: 'free',
        );

        // Act & Assert
        expect(book1, isNot(equals(book2)));
      });
    });
  });
}
```

## 🎨 Widget测试实现

### 1. 页面组件测试

```dart
// test/widget/pages/creation_page_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mockito/mockito.dart';

import 'package:cheestack_flt/features/creation/pages/creation_page.dart';
import 'package:cheestack_flt/features/creation/controllers/creation_controller.dart';

import '../../helpers/widget_test_helpers.dart';
import '../../mocks/mock_services.dart';

void main() {
  group('CreationPage Widget Tests', () {
    late MockCreationController mockController;

    setUp(() {
      mockController = MockCreationController();
      Get.put<CreationController>(mockController);
    });

    tearDown(() {
      Get.reset();
    });

    testWidgets('should display loading indicator when loading', (tester) async {
      // Arrange
      when(mockController.isLoading).thenReturn(true);
      when(mockController.totalBooks).thenReturn(0);
      when(mockController.totalCards).thenReturn(0);
      when(mockController.recentCreations).thenReturn([]);

      // Act
      await tester.pumpWidget(
        createTestWidget(child: CreationPage()),
      );

      // Assert
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('should display stats cards when data loaded', (tester) async {
      // Arrange
      when(mockController.isLoading).thenReturn(false);
      when(mockController.totalBooks).thenReturn(5);
      when(mockController.totalCards).thenReturn(20);
      when(mockController.recentCreations).thenReturn([]);

      // Act
      await tester.pumpWidget(
        createTestWidget(child: CreationPage()),
      );

      // Assert
      expect(find.text('5'), findsOneWidget); // 书籍数量
      expect(find.text('20'), findsOneWidget); // 卡片数量
      expect(find.text('我的书籍'), findsOneWidget);
      expect(find.text('我的卡片'), findsOneWidget);
    });

    testWidgets('should display recent creations list', (tester) async {
      // Arrange
      final recentBooks = [
        BookModel(id: 1, name: 'Recent Book 1'),
        BookModel(id: 2, name: 'Recent Book 2'),
      ];
      
      when(mockController.isLoading).thenReturn(false);
      when(mockController.totalBooks).thenReturn(2);
      when(mockController.totalCards).thenReturn(0);
      when(mockController.recentCreations).thenReturn(recentBooks);

      // Act
      await tester.pumpWidget(
        createTestWidget(child: CreationPage()),
      );

      // Assert
      expect(find.text('Recent Book 1'), findsOneWidget);
      expect(find.text('Recent Book 2'), findsOneWidget);
    });

    testWidgets('should call createBook when FAB is tapped', (tester) async {
      // Arrange
      when(mockController.isLoading).thenReturn(false);
      when(mockController.totalBooks).thenReturn(0);
      when(mockController.totalCards).thenReturn(0);
      when(mockController.recentCreations).thenReturn([]);

      // Act
      await tester.pumpWidget(
        createTestWidget(child: CreationPage()),
      );
      
      await tester.tap(find.byType(FloatingActionButton));
      await tester.pump();

      // Assert
      verify(mockController.createBook()).called(1);
    });
  });
}
```

### 2. 自定义组件测试

```dart
// test/widget/widgets/book_card_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:cheestack_flt/features/creation/pages/widgets/book_card.dart';
import 'package:cheestack_flt/models/book.dart';

import '../../helpers/widget_test_helpers.dart';

void main() {
  group('BookCard Widget Tests', () {
    late BookModel testBook;

    setUp(() {
      testBook = BookModel(
        id: 1,
        name: 'Test Book',
        brief: 'This is a test book description',
        cover: 'https://example.com/cover.jpg',
        privacy: 'free',
        cardCount: 10,
      );
    });

    testWidgets('should display book information', (tester) async {
      // Act
      await tester.pumpWidget(
        createTestWidget(
          child: BookCard(book: testBook),
        ),
      );

      // Assert
      expect(find.text('Test Book'), findsOneWidget);
      expect(find.text('This is a test book description'), findsOneWidget);
      expect(find.text('10张卡片'), findsOneWidget);
    });

    testWidgets('should display default cover when no cover provided', (tester) async {
      // Arrange
      final bookWithoutCover = testBook.copyWith(cover: null);

      // Act
      await tester.pumpWidget(
        createTestWidget(
          child: BookCard(book: bookWithoutCover),
        ),
      );

      // Assert
      expect(find.byIcon(Icons.book), findsOneWidget);
    });

    testWidgets('should call onTap when card is tapped', (tester) async {
      // Arrange
      bool tapped = false;
      void onTap() => tapped = true;

      // Act
      await tester.pumpWidget(
        createTestWidget(
          child: BookCard(
            book: testBook,
            onTap: onTap,
          ),
        ),
      );

      await tester.tap(find.byType(BookCard));
      await tester.pump();

      // Assert
      expect(tapped, isTrue);
    });

    testWidgets('should show action menu when menu button is tapped', (tester) async {
      // Act
      await tester.pumpWidget(
        createTestWidget(
          child: BookCard(book: testBook),
        ),
      );

      await tester.tap(find.byIcon(Icons.more_vert));
      await tester.pumpAndSettle();

      // Assert
      expect(find.text('编辑'), findsOneWidget);
      expect(find.text('复制'), findsOneWidget);
      expect(find.text('删除'), findsOneWidget);
    });
  });
}
```

## 🔗 集成测试实现

### 1. 完整用户流程测试

```dart
// integration_test/book_management_test.dart
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import 'package:cheestack_flt/main.dart' as app;
import 'helpers/test_helpers.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Book Management Integration Tests', () {
    testWidgets('complete book creation flow', (tester) async {
      // 启动应用
      app.main();
      await tester.pumpAndSettle();

      // 登录（如果需要）
      await loginUser(tester);

      // 导航到创作页面
      await tester.tap(find.text('创作'));
      await tester.pumpAndSettle();

      // 点击创建书籍按钮
      await tester.tap(find.byIcon(Icons.add));
      await tester.pumpAndSettle();

      // 填写书籍信息
      await tester.enterText(
        find.byKey(Key('book_name_field')),
        'Integration Test Book',
      );
      await tester.enterText(
        find.byKey(Key('book_brief_field')),
        'This book is created by integration test',
      );

      // 选择隐私设置
      await tester.tap(find.text('公开'));
      await tester.pumpAndSettle();

      // 保存书籍
      await tester.tap(find.text('保存'));
      await tester.pumpAndSettle();

      // 验证书籍创建成功
      expect(find.text('Integration Test Book'), findsOneWidget);
      expect(find.text('书籍创建成功'), findsOneWidget);
    });

    testWidgets('book editing flow', (tester) async {
      // 启动应用并登录
      app.main();
      await tester.pumpAndSettle();
      await loginUser(tester);

      // 导航到书籍列表
      await tester.tap(find.text('我的书籍'));
      await tester.pumpAndSettle();

      // 点击第一本书的编辑按钮
      await tester.tap(find.byIcon(Icons.more_vert).first);
      await tester.pumpAndSettle();
      await tester.tap(find.text('编辑'));
      await tester.pumpAndSettle();

      // 修改书籍名称
      await tester.enterText(
        find.byKey(Key('book_name_field')),
        'Updated Book Name',
      );

      // 保存修改
      await tester.tap(find.text('保存'));
      await tester.pumpAndSettle();

      // 验证修改成功
      expect(find.text('Updated Book Name'), findsOneWidget);
      expect(find.text('书籍更新成功'), findsOneWidget);
    });

    testWidgets('book deletion flow', (tester) async {
      // 启动应用并登录
      app.main();
      await tester.pumpAndSettle();
      await loginUser(tester);

      // 导航到书籍列表
      await tester.tap(find.text('我的书籍'));
      await tester.pumpAndSettle();

      // 记录删除前的书籍数量
      final bookCountBefore = find.byType(BookCard).evaluate().length;

      // 删除第一本书
      await tester.tap(find.byIcon(Icons.more_vert).first);
      await tester.pumpAndSettle();
      await tester.tap(find.text('删除'));
      await tester.pumpAndSettle();

      // 确认删除
      await tester.tap(find.text('确认'));
      await tester.pumpAndSettle();

      // 验证删除成功
      final bookCountAfter = find.byType(BookCard).evaluate().length;
      expect(bookCountAfter, equals(bookCountBefore - 1));
      expect(find.text('书籍删除成功'), findsOneWidget);
    });
  });
}
```

### 2. 数据同步测试

```dart
// integration_test/data_sync_test.dart
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';

import 'package:cheestack_flt/main.dart' as app;
import 'helpers/test_helpers.dart';

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Data Sync Integration Tests', () {
    testWidgets('offline book creation and sync', (tester) async {
      // 启动应用并登录
      app.main();
      await tester.pumpAndSettle();
      await loginUser(tester);

      // 模拟离线状态
      await setNetworkState(false);

      // 创建书籍（离线）
      await createBook(tester, 'Offline Book', 'Created offline');

      // 验证书籍在本地创建成功
      expect(find.text('Offline Book'), findsOneWidget);
      expect(find.text('书籍已保存到本地'), findsOneWidget);

      // 恢复网络连接
      await setNetworkState(true);

      // 触发同步
      await tester.tap(find.byIcon(Icons.sync));
      await tester.pumpAndSettle();

      // 等待同步完成
      await tester.pump(Duration(seconds: 3));

      // 验证同步成功
      expect(find.text('数据同步成功'), findsOneWidget);
    });

    testWidgets('conflict resolution during sync', (tester) async {
      // 启动应用并登录
      app.main();
      await tester.pumpAndSettle();
      await loginUser(tester);

      // 创建书籍
      await createBook(tester, 'Conflict Book', 'Original content');

      // 模拟服务器端修改（通过API或其他客户端）
      await simulateServerUpdate('Conflict Book', 'Server modified content');

      // 本地修改同一本书
      await editBook(tester, 'Conflict Book', 'Local modified content');

      // 触发同步，应该检测到冲突
      await tester.tap(find.byIcon(Icons.sync));
      await tester.pumpAndSettle();

      // 验证冲突解决对话框出现
      expect(find.text('数据冲突'), findsOneWidget);
      expect(find.text('使用本地版本'), findsOneWidget);
      expect(find.text('使用服务器版本'), findsOneWidget);

      // 选择使用本地版本
      await tester.tap(find.text('使用本地版本'));
      await tester.pumpAndSettle();

      // 验证冲突解决成功
      expect(find.text('Local modified content'), findsOneWidget);
    });
  });
}
```

## 📊 测试覆盖率和质量

### 1. 覆盖率配置

```yaml
# test/coverage_helper_test.dart
// Helper file to import all files for coverage
import 'package:cheestack_flt/features/creation/controllers/creation_controller.dart';
import 'package:cheestack_flt/features/creation/controllers/book_controller.dart';
import 'package:cheestack_flt/features/creation/apis/book_service.dart';
import 'package:cheestack_flt/features/creation/apis/card_service.dart';
import 'package:cheestack_flt/models/book.dart';
import 'package:cheestack_flt/models/card_model.dart';

void main() {
  // This file is used to generate coverage report
}
```

### 2. 测试运行脚本

```bash
#!/bin/bash
# scripts/run_tests.sh

echo "Running unit tests..."
flutter test test/unit/ --coverage

echo "Running widget tests..."
flutter test test/widget/ --coverage

echo "Running integration tests..."
flutter test integration_test/ --coverage

echo "Generating coverage report..."
genhtml coverage/lcov.info -o coverage/html

echo "Opening coverage report..."
open coverage/html/index.html
```

### 3. 质量门禁

```yaml
# .github/workflows/test.yml
name: Test

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: subosito/flutter-action@v2
        with:
          flutter-version: '3.16.0'
      
      - name: Install dependencies
        run: flutter pub get
      
      - name: Run tests
        run: flutter test --coverage
      
      - name: Check coverage
        run: |
          COVERAGE=$(lcov --summary coverage/lcov.info | grep -o 'lines......: [0-9.]*%' | grep -o '[0-9.]*')
          echo "Coverage: $COVERAGE%"
          if (( $(echo "$COVERAGE < 80" | bc -l) )); then
            echo "Coverage is below 80%"
            exit 1
          fi
```

---

**注意**：本文档描述的测试策略应根据项目实际情况进行调整。测试用例应该覆盖正常流程、异常情况和边界条件，确保代码的健壮性和可靠性。
